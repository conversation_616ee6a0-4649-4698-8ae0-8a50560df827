{"name": "nougat", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "cd prisma && npx ts-node seed.ts", "export:sql": "node --experimental-modules scripts/export-sql.mjs"}, "dependencies": {"@prisma/client": "^6.6.0", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.11", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "lucide-react": "^0.503.0", "next": "15.3.1", "prisma": "^6.6.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2", "typescript": "^5"}}