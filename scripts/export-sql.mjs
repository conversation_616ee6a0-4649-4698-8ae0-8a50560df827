import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function exportSql() {
  try {
    // Define the updated SQL with UUID types for user IDs
    const updatedSql = `-- Create tables for Kanbany app
CREATE TABLE IF NOT EXISTS "Vision" (
  "id" TEXT PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "userId" UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS "Goal" (
  "id" TEXT PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "visionId" TEXT REFERENCES "Vision"(id) ON DELETE SET NULL,
  "userId" UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS "Tag" (
  "id" TEXT PRIMARY KEY,
  "name" TEXT UNIQUE NOT NULL,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS "Task" (
  "id" TEXT PRIMARY KEY,
  "title" TEXT NOT NULL,
  "description" TEXT,
  "status" TEXT NOT NULL DEFAULT 'monday',
  "date" TEXT,
  "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  "goalId" TEXT REFERENCES "Goal"(id) ON DELETE SET NULL,
  "completed" BOOLEAN NOT NULL DEFAULT false,
  "userId" UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS "_TagToTask" (
  "A" TEXT NOT NULL REFERENCES "Tag"(id) ON DELETE CASCADE,
  "B" TEXT NOT NULL REFERENCES "Task"(id) ON DELETE CASCADE,
  CONSTRAINT "_TagToTask_AB_unique" UNIQUE ("A", "B")
);

-- Create indexes
CREATE INDEX IF NOT EXISTS "Vision_userId_idx" ON "Vision"("userId");
CREATE INDEX IF NOT EXISTS "Goal_visionId_idx" ON "Goal"("visionId");
CREATE INDEX IF NOT EXISTS "Goal_userId_idx" ON "Goal"("userId");
CREATE INDEX IF NOT EXISTS "Task_goalId_idx" ON "Task"("goalId");
CREATE INDEX IF NOT EXISTS "Task_userId_idx" ON "Task"("userId");
CREATE INDEX IF NOT EXISTS "Task_status_idx" ON "Task"("status");
CREATE INDEX IF NOT EXISTS "_TagToTask_A_idx" ON "_TagToTask"("A");
CREATE INDEX IF NOT EXISTS "_TagToTask_B_idx" ON "_TagToTask"("B");

-- Create trigger to update the updatedAt field
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW."updatedAt" = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE TRIGGER set_timestamp_vision
BEFORE UPDATE ON "Vision"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE OR REPLACE TRIGGER set_timestamp_goal
BEFORE UPDATE ON "Goal"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE OR REPLACE TRIGGER set_timestamp_task
BEFORE UPDATE ON "Task"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE OR REPLACE TRIGGER set_timestamp_tag
BEFORE UPDATE ON "Tag"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();`;
    
    console.log('=============================================');
    console.log('SQL MIGRATION STATEMENTS FOR SUPABASE DASHBOARD');
    console.log('=============================================');
    console.log('Copy and paste these statements into the Supabase SQL Editor:');
    console.log('=============================================\n');
    
    console.log(updatedSql);
    
    console.log('\n=============================================');
    console.log('END OF SQL STATEMENTS');
    console.log('=============================================');
    
    // Create a file with the SQL for easy copying
    const outputPath = path.join(__dirname, '../migration-for-dashboard.sql');
    fs.writeFileSync(outputPath, updatedSql);
    
    console.log(`\nSQL has also been saved to: ${outputPath}`);
    console.log('You can open this file and copy its contents to the Supabase SQL Editor');
  } catch (error) {
    console.error('Error exporting SQL:', error);
    process.exit(1);
  }
}

exportSql(); 