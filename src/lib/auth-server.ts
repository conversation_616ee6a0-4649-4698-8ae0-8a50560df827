import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';

// Create a Supabase server client
export async function getServerSupabase() {
  const cookieStore = cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set(name, value, options) {
          // This is a read-only context, so we do nothing
        },
        remove(name, options) {
          // This is a read-only context, so we do nothing
        },
      },
    }
  );
}

// Get the current user's session
export async function getServerSession() {
  const supabase = await getServerSupabase();
  const { data: { session } } = await supabase.auth.getSession();
  return session;
}

// Check if a user is authenticated on the server
export async function requireAuth() {
  const session = await getServerSession();
  return session?.user;
} 