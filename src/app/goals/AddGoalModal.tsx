'use client';

import { useState, useEffect } from 'react';
import { useKanbanStore } from '@/lib/store';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface AddGoalModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AddGoalModal({ isOpen, onClose }: AddGoalModalProps) {
  const { addGoal, visions, setVisions } = useKanbanStore();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedVisionId, setSelectedVisionId] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch visions when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchVisions();
    }
  }, [isOpen]);

  const fetchVisions = async () => {
    try {
      const response = await fetch('/api/visions');
      if (response.ok) {
        const data = await response.json();
        setVisions(data);
      } else {
        console.error('Failed to fetch visions');
      }
    } catch (error) {
      console.error('Error fetching visions:', error);
    }
  };

  const handleSubmit = async () => {
    if (!title.trim()) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/goals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          visionId: selectedVisionId,
        }),
      });
      
      if (response.ok) {
        const newGoal = await response.json();
        addGoal(newGoal);
        resetForm();
        onClose();
        // Refresh the page to update the UI
        window.location.reload();
      } else {
        console.error('Failed to create goal');
      }
    } catch (error) {
      console.error('Error creating goal:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setSelectedVisionId(undefined);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title">Add New Goal</DialogTitle>
        </DialogHeader>
        <div className="dialog-content space-y-4">
          <div className="form-field">
            <label htmlFor="title" className="form-label">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Goal title"
              className="task-dialog-input"
            />
          </div>
          <div className="form-field">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Goal description"
              className="task-dialog-input min-h-[100px]"
            />
          </div>
          <div className="form-field">
            <label htmlFor="vision" className="form-label">
              Vision
            </label>
            <select
              id="vision"
              value={selectedVisionId || ''}
              onChange={(e) => setSelectedVisionId(e.target.value || undefined)}
              className="flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a vision</option>
              {visions.map((vision) => (
                <option key={vision.id} value={vision.id}>
                  {vision.title}
                </option>
              ))}
            </select>
          </div>
        </div>
        <DialogFooter className="dialog-footer">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Creating...' : 'Create Goal'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
