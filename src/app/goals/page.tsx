'use client';

import { useState, useEffect } from 'react';
import { useKanbanStore, Goal } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent, CardFooter } from '@/components/ui/card';
import { PlusCircle, Edit, Trash } from 'lucide-react';
import AddGoalModal from './AddGoalModal';
import EditGoalModal from './EditGoalModal';
import { Navbar } from "@/components/ui/navbar";

export default function GoalsPage() {
  const { goals, setGoals } = useKanbanStore();
  const [isAddGoalModalOpen, setIsAddGoalModalOpen] = useState(false);
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [isEditGoalModalOpen, setIsEditGoalModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch goals from the API
  useEffect(() => {
    const fetchGoals = async () => {
      try {
        const response = await fetch('/api/goals');
        if (response.ok) {
          const data = await response.json();
          setGoals(data);
        } else {
          console.error('Failed to fetch goals');
        }
      } catch (error) {
        console.error('Error fetching goals:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchGoals();
  }, [setGoals]);

  const handleDeleteGoal = async (goalId: string) => {
    if (confirm('Are you sure you want to delete this goal?')) {
      try {
        const response = await fetch(`/api/goals/${goalId}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          // Refresh the page to update the UI
          window.location.reload();
        } else {
          console.error('Failed to delete goal');
        }
      } catch (error) {
        console.error('Error deleting goal:', error);
      }
    }
  };

  const handleEditGoal = (goal: Goal) => {
    setSelectedGoal(goal);
    setIsEditGoalModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="relative flex min-h-screen flex-col scrollbar-hide">
        <Navbar />
        <div className="flex justify-center items-center h-screen">Loading...</div>
      </div>
    );
  }

  return (
    <div className="relative flex min-h-screen flex-col scrollbar-hide">
      <Navbar />
      <div className="h-screen flex flex-col overflow-y-auto scrollbar-hide bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 w-full">
          <div className="flex justify-between items-center mb-6 bg-emerald-600/10 p-4 rounded-lg shadow-inner">
            <div>
              <h1 className="text-2xl font-bold text-teal-400">Goals</h1>
              <p className="text-gray-400">Manage your goals and link them to visions</p>
            </div>
            <Button 
              onClick={() => setIsAddGoalModalOpen(true)}
              className="h-9 px-4 rounded-full cursor-pointer bg-emerald-600/80 hover:bg-emerald-600 transition-colors duration-200"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Goal
            </Button>
          </div>
        </div>
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex-1">
          {goals.length === 0 ? (
            <div className="text-center py-12 bg-emerald-600/5 rounded-lg shadow-inner">
              <h2 className="text-xl font-semibold text-teal-400">No goals yet</h2>
              <p className="mt-2 text-gray-400">Create your first goal to get started</p>
              <Button 
                className="mt-4 bg-emerald-600/80 hover:bg-emerald-600 transition-colors duration-200 rounded-full" 
                onClick={() => setIsAddGoalModalOpen(true)}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Goal
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {goals.map((goal) => (
                <Card key={goal.id} className="border border-emerald-500/20 bg-emerald-600/5 hover:bg-emerald-600/10 transition-colors duration-200 shadow-md">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-teal-400">{goal.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400">{goal.description}</p>
                    {goal.vision && (
                      <div className="mt-4 p-2 bg-emerald-600/10 rounded-lg">
                        <p className="text-sm font-medium text-teal-400">Vision:</p>
                        <p className="text-gray-300">{goal.vision.title}</p>
                      </div>
                    )}
                    <div className="mt-4 p-2 bg-emerald-600/10 rounded-lg">
                      <p className="text-sm font-medium text-teal-400">Tasks:</p>
                      <p className="text-gray-300">
                        {goal.tasks ? goal.tasks.length : 0} tasks linked to this goal
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditGoal(goal)}
                      className="text-teal-400 hover:text-teal-300 hover:bg-emerald-600/20"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteGoal(goal.id)}
                      className="text-teal-400 hover:text-teal-300 hover:bg-emerald-600/20"
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </main>

        <AddGoalModal
          isOpen={isAddGoalModalOpen}
          onClose={() => setIsAddGoalModalOpen(false)}
        />

        {selectedGoal && (
          <EditGoalModal
            isOpen={isEditGoalModalOpen}
            onClose={() => setIsEditGoalModalOpen(false)}
            goal={selectedGoal}
          />
        )}
      </div>
    </div>
  );
}
