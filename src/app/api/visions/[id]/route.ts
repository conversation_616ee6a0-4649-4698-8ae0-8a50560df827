import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { updateVision, deleteVision } from '@/lib/supabase-data';
import { Goal, Task, Tag } from '@/lib/store';

// GET /api/visions/[id] - Get a specific vision
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data, error } = await supabase
      .from('Vision')
      .select(`
        *,
        goals:Goal(
          *,
          tasks:Task(
            *,
            tags:_TagToTask(
              tag:Tag(*)
            )
          )
        )
      `)
      .eq('id', params.id)
      .single();
    
    if (error) {
      return NextResponse.json({ error: 'Vision not found' }, { status: 404 });
    }
    
    // Transform the data to match the expected format
    const goals = data.goals?.map((goal: any) => {
      const tasks = goal.tasks?.map((task: any) => {
        const tags = task.tags?.map((tagRel: any) => tagRel.tag) || [];
        return {
          ...task,
          tags
        };
      }) || [];
      
      return {
        ...goal,
        tasks
      };
    }) || [];
    
    const vision = {
      ...data,
      goals
    };
    
    return NextResponse.json(vision);
  } catch (error) {
    console.error('Error fetching vision:', error);
    return NextResponse.json({ error: 'Failed to fetch vision' }, { status: 500 });
  }
}

// PUT /api/visions/[id] - Update a vision
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { title, description } = body;
    
    const vision = await updateVision(params.id, { title, description });
    
    return NextResponse.json(vision);
  } catch (error) {
    console.error('Error updating vision:', error);
    return NextResponse.json({ error: 'Failed to update vision' }, { status: 500 });
  }
}

// DELETE /api/visions/[id] - Delete a vision
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await deleteVision(params.id);
    
    return NextResponse.json({ message: 'Vision deleted successfully' });
  } catch (error) {
    console.error('Error deleting vision:', error);
    return NextResponse.json({ error: 'Failed to delete vision' }, { status: 500 });
  }
}
