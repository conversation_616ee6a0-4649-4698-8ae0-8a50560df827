import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { updateGoal, deleteGoal } from '@/lib/supabase-data';

// GET /api/goals/[id] - Get a specific goal
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data, error } = await supabase
      .from('Goal')
      .select(`
        *,
        vision:Vision(*),
        tasks:Task(
          *,
          tags:_TagToTask(
            tag:Tag(*)
          )
        )
      `)
      .eq('id', params.id)
      .single();
    
    if (error) {
      return NextResponse.json({ error: 'Goal not found' }, { status: 404 });
    }
    
    // Transform the data to match the expected format
    const tasks = data.tasks?.map((task: any) => {
      const tags = task.tags?.map((tagRel: any) => tagRel.tag) || [];
      return {
        ...task,
        tags
      };
    }) || [];
    
    const goal = {
      ...data,
      tasks
    };
    
    return NextResponse.json(goal);
  } catch (error) {
    console.error('Error fetching goal:', error);
    return NextResponse.json({ error: 'Failed to fetch goal' }, { status: 500 });
  }
}

// PUT /api/goals/[id] - Update a goal
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { title, description, visionId } = body;
    
    const goal = await updateGoal(params.id, { title, description, visionId });
    
    return NextResponse.json(goal);
  } catch (error) {
    console.error('Error updating goal:', error);
    return NextResponse.json({ error: 'Failed to update goal' }, { status: 500 });
  }
}

// DELETE /api/goals/[id] - Delete a goal
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await deleteGoal(params.id);
    
    return NextResponse.json({ message: 'Goal deleted successfully' });
  } catch (error) {
    console.error('Error deleting goal:', error);
    return NextResponse.json({ error: 'Failed to delete goal' }, { status: 500 });
  }
}
