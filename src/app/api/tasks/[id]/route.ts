import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { updateTask, deleteTask } from '@/lib/supabase-data';

// GET /api/tasks/[id] - Get a specific task
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data, error } = await supabase
      .from('Task')
      .select(`
        *,
        goal:Goal(*),
        tags:_TagToTask(
          tag:Tag(*)
        )
      `)
      .eq('id', params.id)
      .single();
    
    if (error) {
      return NextResponse.json({ error: 'Task not found' }, { status: 404 });
    }
    
    // Transform the data to match the expected format
    const tags = data.tags?.map((tagRel: { tag: Record<string, unknown> }) => tagRel.tag) || [];
    
    const task = {
      ...data,
      tags
    };
    
    return NextResponse.json(task);
  } catch (error) {
    console.error('Error fetching task:', error);
    return NextResponse.json({ error: 'Failed to fetch task' }, { status: 500 });
  }
}

// PUT /api/tasks/[id] - Update a task
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { title, description, status, goalId, tags, date, completed } = body;
    
    const task = await updateTask(params.id, {
      title,
      description,
      status,
      goalId,
      date,
      completed,
      tags
    });
    
    return NextResponse.json(task);
  } catch (error) {
    console.error('Error updating task:', error);
    return NextResponse.json({ error: 'Failed to update task' }, { status: 500 });
  }
}

// DELETE /api/tasks/[id] - Delete a task
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await deleteTask(params.id);
    
    return NextResponse.json({ message: 'Task deleted successfully' });
  } catch (error) {
    console.error('Error deleting task:', error);
    return NextResponse.json({ error: 'Failed to delete task' }, { status: 500 });
  }
}
