'use client';

import { useState, useEffect } from 'react';
import { useKanbanStore, Vision } from '@/lib/store';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface EditVisionModalProps {
  isOpen: boolean;
  onClose: () => void;
  vision: Vision;
}

export default function EditVisionModal({ isOpen, onClose, vision }: EditVisionModalProps) {
  const { updateVision } = useKanbanStore();
  const [title, setTitle] = useState(vision.title);
  const [description, setDescription] = useState(vision.description || '');
  const [isLoading, setIsLoading] = useState(false);

  // Update form when vision changes
  useEffect(() => {
    setTitle(vision.title);
    setDescription(vision.description || '');
  }, [vision]);

  const handleSubmit = async () => {
    if (!title.trim()) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/visions/${vision.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
        }),
      });
      
      if (response.ok) {
        const updatedVision = await response.json();
        updateVision(vision.id, updatedVision);
        onClose();
        // Refresh the page to update the UI
        window.location.reload();
      } else {
        console.error('Failed to update vision');
      }
    } catch (error) {
      console.error('Error updating vision:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title">Edit Vision</DialogTitle>
        </DialogHeader>
        <div className="dialog-content space-y-4">
          <div className="form-field">
            <label htmlFor="title" className="form-label">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Vision title"
              className="task-dialog-input"
            />
          </div>
          <div className="form-field">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Vision description"
              className="task-dialog-input min-h-[100px]"
            />
          </div>
        </div>
        <DialogFooter className="dialog-footer">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
