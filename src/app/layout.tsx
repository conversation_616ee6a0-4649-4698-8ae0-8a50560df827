import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { BrainDumpProvider } from "@/components/brain-dump/BrainDump";
import { AuthProvider } from "@/lib/auth";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Nougat - Organize Tasks, Goals, and Visions",
  description: "A productivity application that connects your daily tasks to meaningful goals and long-term visions",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="scrollbar-hide">
      <body className={`${inter.variable} font-sans antialiased min-h-screen scrollbar-hide`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <BrainDumpProvider>
              {children}
            </BrainDumpProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
