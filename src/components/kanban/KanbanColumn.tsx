'use client';

import { useDrop } from 'react-dnd';
import { Task } from '@/lib/store';
import TaskCard from './TaskCard';
import { useRef, useEffect } from 'react';

type DayOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

interface KanbanColumnProps {
  title: string;
  tasks: Task[];
  status: DayOfWeek;
  onMoveTask: (taskId: string, newStatus: DayOfWeek) => void;
  dateDisplay?: string;
  isToday?: boolean;
}

export default function KanbanColumn({
  title,
  tasks,
  status,
  onMoveTask,
  dateDisplay,
  isToday = false,
}: KanbanColumnProps) {
  const columnRef = useRef<HTMLDivElement>(null);
  
  // Set up drop target
  const [{ isOver }, drop] = useDrop({
    accept: 'TASK',
    drop: (item: { id: string }) => {
      onMoveTask(item.id, status);
    },
    collect: (monitor) => ({
      isOver: !!monitor.isOver(),
    }),
  });
  
  // Connect the drop ref
  useEffect(() => {
    if (columnRef.current) {
      drop(columnRef.current);
    }
  }, [drop]);

  return (
    <div
      ref={columnRef}
      className={`kanban-column flex flex-col h-full ${isOver ? 'bg-opacity-80' : ''}`}
    >
      <div className="kanban-column-header">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <h3 className={`kanban-column-title ${
              isToday 
                ? 'text-amber-300 font-semibold bg-primary/10 px-2 py-1 rounded-md' 
                : ''
            }`}>{title}</h3>
            <span className="kanban-column-count ml-2">{tasks.length}</span>
          </div>
          {dateDisplay && (
            <div className={`text-sm font-medium ${isToday ? 'text-primary bg-primary/10 px-2 py-0.5 rounded-full' : 'text-gray-400'}`}>
              {dateDisplay}
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto pb-0 min-h-0 scrollbar-hide">
        {tasks.length > 0 ? (
          <div className="space-y-2 h-full">
            {tasks.map((task) => (
              <TaskCard key={task.id} task={task} />
            ))}
          </div>
        ) : (
          <div className="empty-column-state h-full">
            <p className="text-gray-400 text-sm">No tasks</p>
          </div>
        )}
      </div>
    </div>
  );
}
