'use client';

import { useState, useEffect } from 'react';
import { useKanbanStore, Task, Tag } from '@/lib/store';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

interface EditTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: Task;
}

export default function EditTaskModal({ isOpen, onClose, task }: EditTaskModalProps) {
  const { updateTask, goals, setGoals, tags, setTags } = useKanbanStore();
  const [title, setTitle] = useState(task.title);
  const [description, setDescription] = useState(task.description || '');
  const [selectedGoalId, setSelectedGoalId] = useState<string | undefined>(task.goalId);
  const [selectedTags, setSelectedTags] = useState<Tag[]>(task.tags || []);
  const [newTag, setNewTag] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Fetch goals and tags when the modal opens
  useEffect(() => {
    if (isOpen) {
      fetchGoals();
      fetchTags();
    }
  }, [isOpen]);

  // Update form when task changes
  useEffect(() => {
    setTitle(task.title);
    setDescription(task.description || '');
    setSelectedGoalId(task.goalId);
    setSelectedTags(task.tags || []);
  }, [task]);

  const fetchGoals = async () => {
    try {
      const response = await fetch('/api/goals');
      if (response.ok) {
        const data = await response.json();
        setGoals(data);
      } else {
        console.error('Failed to fetch goals');
      }
    } catch (error) {
      console.error('Error fetching goals:', error);
    }
  };

  const fetchTags = async () => {
    try {
      const response = await fetch('/api/tags');
      if (response.ok) {
        const data = await response.json();
        setTags(data);
      } else {
        console.error('Failed to fetch tags');
      }
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() === '') return;
    
    // Check if tag already exists in selected tags
    if (selectedTags.some((tag) => tag.name.toLowerCase() === newTag.toLowerCase())) {
      setNewTag('');
      return;
    }
    
    // Check if tag exists in all tags
    const existingTag = tags.find(
      (tag) => tag.name.toLowerCase() === newTag.toLowerCase()
    );
    
    if (existingTag) {
      setSelectedTags([...selectedTags, existingTag]);
    } else {
      // Create a temporary tag with a temporary ID
      const tempTag: Tag = {
        id: `temp-${Date.now()}`,
        name: newTag,
      };
      setSelectedTags([...selectedTags, tempTag]);
    }
    
    setNewTag('');
  };

  const handleRemoveTag = (tagToRemove: Tag) => {
    setSelectedTags(selectedTags.filter((tag) => tag.id !== tagToRemove.id));
  };

  const handleSubmit = async () => {
    if (!title.trim()) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          description,
          status: task.status,
          goalId: selectedGoalId,
          tags: selectedTags.map((tag) => ({ name: tag.name })),
          completed: task.completed ?? false,
        }),
      });
      
      if (response.ok) {
        const updatedTask = await response.json();
        updateTask(task.id, updatedTask);
        onClose();
      } else {
        console.error('Failed to update task');
      }
    } catch (error) {
      console.error('Error updating task:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px] task-dialog border-0">
        <DialogHeader className="dialog-header">
          <DialogTitle className="dialog-title">Edit Task</DialogTitle>
        </DialogHeader>
        <div className="dialog-content space-y-4">
          <div className="form-field">
            <label htmlFor="title" className="form-label">
              Title
            </label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Task title"
              className="task-dialog-input"
            />
          </div>
          <div className="form-field">
            <label htmlFor="description" className="form-label">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Task description"
              className="task-dialog-input min-h-[100px]"
            />
          </div>
          <div className="form-field">
            <label htmlFor="goal" className="form-label">
              Goal
            </label>
            <select
              id="goal"
              value={selectedGoalId || ''}
              onChange={(e) => setSelectedGoalId(e.target.value || undefined)}
              className="flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <option value="">Select a goal</option>
              {goals.map((goal) => (
                <option key={goal.id} value={goal.id}>
                  {goal.title}
                </option>
              ))}
            </select>
          </div>
          <div className="form-field">
            <label htmlFor="tags" className="form-label">
              Tags
            </label>
            <div className="flex gap-2">
              <Input
                id="tags"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                className="task-dialog-input"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <Button type="button" onClick={handleAddTag} size="sm" className="px-4">
                Add
              </Button>
            </div>
            {selectedTags.length > 0 && (
              <div className="flex flex-wrap gap-1.5 mt-3">
                {selectedTags.map((tag) => (
                  <Badge key={tag.id} variant="secondary" className="flex items-center gap-1 py-1 px-2">
                    {tag.name}
                    <X
                      className="h-3 w-3 cursor-pointer ml-1 opacity-70 hover:opacity-100"
                      onClick={() => handleRemoveTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </div>
        <DialogFooter className="dialog-footer">
          <Button variant="outline" onClick={onClose} size="sm">
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading} size="sm">
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
