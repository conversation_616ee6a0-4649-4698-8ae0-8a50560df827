'use client';

import { useEffect, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useKanbanStore, Task } from '@/lib/store';
import KanbanColumn from './KanbanColumn';
import AddTaskModal from './AddTaskModal';
import { Button } from '@/components/ui/button';
import { Loader2, PlusCircle, ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { format, startOfWeek, endOfWeek, addWeeks, getWeek, isSameDay, parseISO, isWithinInterval } from 'date-fns';

// Define types for better type safety
type DayOfWeek = 'saturday' | 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday';
type TaskStatus = DayOfWeek;

export default function KanbanBoard() {
  const { tasks, setTasks, moveTask } = useKanbanStore();
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [weekOffset, setWeekOffset] = useState(0);

  // Calculate current week start and end dates - starting on Saturday (index 6)
  const weekStart = startOfWeek(addWeeks(new Date(), weekOffset), { weekStartsOn: 6 });
  const weekEnd = endOfWeek(addWeeks(new Date(), weekOffset), { weekStartsOn: 6 });
  const weekNumber = getWeek(weekStart, { weekStartsOn: 6 });
  
  // Format dates for display
  const dateRangeText = `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d, yyyy')}`;

  // Days of the week configuration with corresponding dates, starting from Saturday
  const daysWithDates = [0, 1, 2, 3, 4, 5, 6].map(dayOffset => {
    const date = new Date(weekStart);
    date.setDate(date.getDate() + dayOffset);

    // Adjust dayIndex to map correctly starting from Saturday
    // date-fns getDay() returns 0 for Sunday, 1 for Monday, ..., 6 for Saturday
    const dayIndex = date.getDay(); // 0 = Sunday, ..., 6 = Saturday
    const dayName = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][dayIndex] as DayOfWeek;

    return {
      name: dayName,
      date: date,
      dateString: format(date, 'yyyy-MM-dd'),
      displayDate: format(date, 'd'),
      isToday: isSameDay(date, new Date())
    };
  }).sort((a, b) => {
    // Ensure correct order: Saturday, Sunday, Monday, ... Friday
    const dayOrder: DayOfWeek[] = ['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
    return dayOrder.indexOf(a.name) - dayOrder.indexOf(b.name);
  });

  // Navigation functions
  const goToPreviousWeek = () => setWeekOffset(weekOffset - 1);
  const goToNextWeek = () => setWeekOffset(weekOffset + 1);
  const goToCurrentWeek = () => setWeekOffset(0);

  // Fetch tasks from the API
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        const response = await fetch('/api/tasks');
        if (response.ok) {
          const data = await response.json();
          
          // Ensure all tasks have a date property
          const tasksWithDates = data.map((task: Task) => {
            if (!task.date) {
              // If there's no date, we'll add today's date
              const today = new Date();
              return { ...task, date: format(today, 'yyyy-MM-dd') };
            }
            return task;
          });
          
          setTasks(tasksWithDates);
        } else {
          console.error('Failed to fetch tasks');
        }
      } catch (error) {
        console.error('Error fetching tasks:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTasks();
  }, [setTasks]);

  // Filter tasks by day of the week AND date range
  const tasksByDay: Record<DayOfWeek, typeof tasks> = {
    'saturday': [],
    'sunday': [],
    'monday': [],
    'tuesday': [],
    'wednesday': [],
    'thursday': [],
    'friday': []
  };

  // Organize tasks into the right day bins
  tasks.forEach(task => {
    // Find which day this task belongs to based on date and status
    if (task.date) {
      const taskDate = parseISO(task.date);
      
      // If the task date is within the current week range, show it on the right day
      if (isWithinInterval(taskDate, { start: weekStart, end: weekEnd })) {
        const dayOfWeek = format(taskDate, 'EEEE').toLowerCase() as DayOfWeek;
        tasksByDay[dayOfWeek].push(task);
      }
    } else {
      // Fallback for tasks without dates - use status
      tasksByDay[task.status].push(task);
    }
  });

  // Handle task movement between columns
  const handleMoveTask = async (taskId: string, newStatus: TaskStatus, date: Date) => {
    try {
      // Format date for API
      const formattedDate = format(date, 'yyyy-MM-dd');
      
      // Optimistically update the UI
      moveTask(taskId, newStatus);
      
      // Also update the date in our local state
      const taskToUpdate = tasks.find(t => t.id === taskId);
      if (taskToUpdate) {
        useKanbanStore.getState().updateTask(taskId, { 
          status: newStatus,
          date: formattedDate
        });
      }

      // Update the task in the database
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          date: formattedDate
        }),
      });

      if (!response.ok) {
        console.error('Failed to update task status');
        // Revert the optimistic update if the API call fails
        const task = tasks.find((t) => t.id === taskId);
        if (task) {
          moveTask(taskId, task.status);
        }
      }
    } catch (error) {
      console.error('Error updating task status:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-gray-300">Loading...</span>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col overflow-y-auto scrollbar-hide">
        {/* Header with week navigation and add task button */}
        <div className="flex justify-between items-center p-4 border-b border-gray-800">
          {/* Week navigation */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={goToPreviousWeek} 
                className="p-1 hover:bg-gray-800"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
              
              <div className="flex items-center mx-2">
                <Calendar className="h-4 w-4 mr-2 text-primary" />
                <div>
                  <span className="font-medium text-white">Week {weekNumber}</span>
                  <span className="ml-2 text-gray-400">{dateRangeText}</span>
                </div>
              </div>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={goToNextWeek} 
                className="p-1 hover:bg-gray-800"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>
            
            {weekOffset !== 0 && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={goToCurrentWeek} 
                className="text-xs px-2 py-1 h-8 bg-gray-800 border-gray-700 hover:bg-gray-700"
              >
                Current Week
              </Button>
            )}
          </div>
          
          {/* Add task button */}
          <Button 
            onClick={() => setIsAddTaskModalOpen(true)}
            className="h-9 px-4 rounded-full cursor-pointer hover:bg-amber-600"
          >
            <PlusCircle className="h-6 w-6 mr-2 transition-colors" />
            <span className="hidden sm:inline">Add Task</span>
          </Button>
        </div>

        {/* Full-screen column grid */}
        <div className="flex-1 overflow-y-auto scrollbar-hide">
          <div className="grid grid-cols-7 gap-0 h-full">
            {daysWithDates.map(day => (
              <KanbanColumn
                key={day.name}
                title={day.name.charAt(0).toUpperCase() + day.name.slice(1)}
                tasks={tasksByDay[day.name]}
                status={day.name}
                onMoveTask={(taskId, status) => handleMoveTask(taskId, status, day.date)}
                dateDisplay={day.displayDate}
                isToday={day.isToday}
              />
            ))}
          </div>
        </div>

        <AddTaskModal
          isOpen={isAddTaskModalOpen}
          onClose={() => setIsAddTaskModalOpen(false)}
          preselectedDate={format(new Date(), 'yyyy-MM-dd')}
        />
      </div>
    </DndProvider>
  );
}
