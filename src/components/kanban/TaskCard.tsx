'use client';

import { useState, useRef, useEffect } from 'react';
import { useDrag } from 'react-dnd';
import { Task, useKanbanStore } from '@/lib/store';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, GripVertical, Trash, MoreVertical, CheckCircle, Circle } from 'lucide-react';
import EditTaskModal from './EditTaskModal';
import Image from 'next/image';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";

interface TaskCardProps {
  task: Task;
}

export default function TaskCard({ task }: TaskCardProps) {
  const { deleteTask, updateTask } = useKanbanStore();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isCompleting, setIsCompleting] = useState(false);

  // Set up drag source
  const [{ isDragging }, dragRef] = useDrag({
    type: 'TASK',
    item: { id: task.id },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  });

  // Create a ref for the drag element
  const dragElementRef = useRef<HTMLDivElement>(null);

  // Connect the drag ref to the element
  useEffect(() => {
    if (dragElementRef.current) {
      dragRef(dragElementRef.current);
    }
  }, [dragRef]);

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update local state using Zustand store
        deleteTask(task.id);
        setIsDeleteDialogOpen(false);
      } else {
        console.error('Failed to delete task');
      }
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  };

  const handleToggleComplete = async () => {
    setIsCompleting(true);
    try {
      // Optimistically update
      updateTask(task.id, { completed: !task.completed });
      const response = await fetch(`/api/tasks/${task.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...task,
          completed: !task.completed,
          tags: task.tags?.map(tag => ({ name: tag.name })),
          goalId: task.goalId,
        }),
      });
      if (!response.ok) {
        // Revert if failed
        updateTask(task.id, { completed: task.completed });
      }
    } catch {
      updateTask(task.id, { completed: task.completed });
    } finally {
      setIsCompleting(false);
    }
  };

  return (
    <>
      <div 
        ref={dragElementRef}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <Card
          className={`task-card cursor-grab border-0  ${isDragging ? 'task-card-drageffect' : ''} ${task.completed ? 'opacity-50 pointer-events-auto' : ''}`}
        >
          <div className="absolute left-2 top-2 z-10">
            <button
              aria-label={task.completed ? 'Mark as incomplete' : 'Mark as complete'}
              onClick={handleToggleComplete}
              disabled={isCompleting}
              className="focus:outline-none"
            >
              {task.completed ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <Circle className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          <div className="absolute right-2 top-2 opacity-0 transition-opacity group-hover:opacity-100">
            <GripVertical className={`h-4 w-4 icon-grip transition-opacity ${isHovered ? 'opacity-80' : 'opacity-0'}`} />
          </div>
          
          <CardHeader className="pb-0.5 px-4 pt-3 pl-10 border-0">
            <CardTitle className="group flex text-[13px] items-start justify-between">
              {task.title}
            </CardTitle>
          </CardHeader>
          
          <CardContent className="task-card-content px-4 py-3 pb-3">
            <div className="flex items-center justify-between mb-2">
              <div className="flex flex-wrap gap-1">
                {task.tags && task.tags.length > 0 && (
                  task.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag.id} variant="secondary" className="badge-secondary text-[11px] py-0 px-2 h-5">
                      {tag.name}
                    </Badge>
                  ))
                )}
                {task.tags && task.tags.length > 3 && (
                  <Badge variant="outline" className="badge-outline text-xs py-0 px-1.5 h-5">
                    +{task.tags.length - 3}
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="mt-5 flex items-center gap-3">
              {task.goal && (
                <Popover>
                  <PopoverTrigger asChild>
                    <button className="flex items-center cursor-pointer focus:outline-none">
                      <Image 
                        src="/assets/icons/goal.png" 
                        alt="Goal" 
                        width={15} 
                        height={15} 
                        className="opacity-90"
                      />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="shadow-lg">
                    <div className="font-medium mb-1 text-xs">Goal</div>
                    <div className="text-xs">{task.goal.title}</div>
                  </PopoverContent>
                </Popover>
              )}
              
              {task.description && (
                <Popover>
                  <PopoverTrigger asChild>
                    <button className="flex items-center cursor-pointer focus:outline-none">
                      <Image 
                        src="/assets/icons/notes.png" 
                        alt="Description" 
                        width={15} 
                        height={15} 
                        className="opacity-90"
                      />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="shadow-lg">
                    <div className="font-medium mb-1 text-xs">Description</div>
                    <div className="text-xs line-clamp-12">{task.description}</div>
                  </PopoverContent>
                </Popover>
              )}
            </div>
            
            <div className="absolute bottom-1 right-0">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 rounded-full border-0 hover:bg-transparent focus:ring-0 focus:ring-offset-0 focus-visible:outline-none focus-visible:ring-0"
                  >
                    <MoreVertical className="h-4 w-4 text-muted-foreground cursor-pointer" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-32 border-0 shadow-lg bg-gray-800 dropdown-menu-content">
                  <DropdownMenuItem
                    className="flex items-center gap-2 text-xs cursor-pointer"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      setIsEditModalOpen(true);
                    }}
                  >
                    <Edit className="h-3.5 w-3.5 icon-edit" />
                    <span>Edit Task</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="flex items-center gap-2 text-xs cursor-pointer text-destructive focus:text-destructive"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      setIsDeleteDialogOpen(true);
                    }}
                  >
                    <Trash className="h-3.5 w-3.5 icon-delete" />
                    <span>Delete Task</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardContent>
        </Card>
      </div>

      <EditTaskModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        task={task}
      />

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px] task-dialog border-0">
          <DialogHeader className="dialog-header">
            <DialogTitle className="dialog-title">Delete Task</DialogTitle>
            <DialogDescription className="mt-1.5 text-sm text-gray-400">
              Are you sure you want to delete this task? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)} 
              size="sm" 
              className="cursor-pointer hover:bg-gray-700 hover:text-gray-100 transition-colors"
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              className="cursor-pointer hover:bg-red-700 transition-colors" 
              onClick={handleDelete} 
              size="sm"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}